import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      // Configuração para imports absolutos baseados em 'src'
      '@': resolve(__dirname, './src'),
      'components': resolve(__dirname, './src/components'),
      'pages': resolve(__dirname, './src/pages'),
      'services': resolve(__dirname, './src/services'),
      'store': resolve(__dirname, './src/store'),
      'types': resolve(__dirname, './src/types'),
      'helpers': resolve(__dirname, './src/helpers'),
      'constants': resolve(__dirname, './src/constants'),
      'assets': resolve(__dirname, './src/assets'),
      'icons': resolve(__dirname, './src/icons'),
      'styles': resolve(__dirname, './src/styles'),
      'theme': resolve(__dirname, './src/theme'),
      'auth': resolve(__dirname, './src/auth'),
    },
  },
  server: {
    port: 3000,
    open: true,
  },
  build: {
    outDir: 'build',
    sourcemap: true,
  },
  define: {
    // Substituir process.env por import.meta.env
    'process.env': 'import.meta.env',
  },
})
